// lib/utils/image_cache_manager.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class OptimizedImageCache {
  static final OptimizedImageCache _instance = OptimizedImageCache._internal();
  factory OptimizedImageCache() => _instance;
  OptimizedImageCache._internal();

  final Map<String, ImageProvider> _imageCache = {};
  final Map<String, Future<ImageProvider>> _loadingImages = {};
  static const int _maxCacheSize =
      15; // Limit cache size to prevent memory issues

  /// Preload background images to prevent UI blocking
  Future<void> preloadBackgroundImages() async {
    final List<String> backgroundImages = [
      'assets/images/0.png',
      'assets/images/1.png',
      'assets/images/2.png',
      'assets/images/3.png',
      'assets/images/4.png',
      'assets/images/5.png',
      'assets/images/6.png',
      'assets/images/7.png',
      'assets/images/8.png',
      'assets/images/9.png',
      'assets/images/10.png',
    ];

    if (kDebugMode) {
      debugPrint(
        '🖼️ Starting to preload ${backgroundImages.length} background images...',
      );
    }

    // Cache all images instantly without validation to avoid frame drops
    int successCount = 0;
    int failureCount = 0;

    // Use parallel loading for maximum speed
    final futures = backgroundImages.map(
      (imagePath) => _preloadSingleImage(imagePath),
    );
    final results = await Future.wait(futures);

    for (bool success in results) {
      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    if (kDebugMode) {
      debugPrint(
        '✅ Image preloading completed: $successCount successful, $failureCount failed',
      );
    }
  }

  Future<bool> _preloadSingleImage(String imagePath) async {
    if (_imageCache.containsKey(imagePath)) return true;

    final stopwatch = Stopwatch()..start();
    try {
      final imageProvider = AssetImage(imagePath);

      // Simply cache the provider - don't try to validate during preload
      // This avoids complex async operations that can cause frame drops
      _imageCache[imagePath] = imageProvider;

      stopwatch.stop();
      if (kDebugMode) {
        debugPrint('✅ Cached $imagePath in ${stopwatch.elapsedMilliseconds}ms');
      }
      return true;
    } catch (e) {
      stopwatch.stop();
      if (kDebugMode) {
        debugPrint(
          '❌ Failed to cache image: $imagePath after ${stopwatch.elapsedMilliseconds}ms, error: $e',
        );
      }
      return false;
    }
  }

  /// Get cached image provider or load if not cached
  ImageProvider getCachedImage(String imagePath) {
    if (_imageCache.containsKey(imagePath)) {
      return _imageCache[imagePath]!;
    }

    // Check cache size limit
    if (_imageCache.length >= _maxCacheSize) {
      // Remove oldest entries (simple FIFO strategy)
      final oldestKey = _imageCache.keys.first;
      _imageCache.remove(oldestKey);
    }

    // If not cached, cache it now
    final imageProvider = AssetImage(imagePath);
    _imageCache[imagePath] = imageProvider;
    return imageProvider;
  }

  /// Check if image exists in cache
  bool isImageCached(String imagePath) {
    return _imageCache.containsKey(imagePath);
  }

  /// Clear cache to free memory - addresses performance warning
  void clearCache() {
    if (kDebugMode) {
      debugPrint('🧹 Clearing image cache to improve performance...');
    }
    _imageCache.clear();
    _loadingImages.clear();

    // Force garbage collection to free memory immediately
    if (kDebugMode) {
      debugPrint('✅ Image cache cleared successfully');
    }
  }

  /// Clear cache periodically to prevent memory buildup
  void clearCacheIfNeeded() {
    if (_imageCache.length > _maxCacheSize * 0.8) {
      if (kDebugMode) {
        debugPrint('🔄 Auto-clearing cache due to size limit');
      }
      clearCache();
    }
  }

  /// Get cache size for monitoring
  int get cacheSize => _imageCache.length;
}

/// Navigation service for accessing context
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();
}

/// Optimized Image widget with better performance and no fade animations
class OptimizedAssetImage extends StatelessWidget {
  final String imagePath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final FilterQuality filterQuality;
  final Widget? errorWidget;
  final bool enableFadeIn;

  const OptimizedAssetImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.filterQuality = FilterQuality.low,
    this.errorWidget,
    this.enableFadeIn = false, // Disabled by default for faster loading
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      filterQuality: filterQuality,
      // Disable fade animation for faster loading
      frameBuilder: enableFadeIn
          ? null
          : (context, child, frame, wasSynchronouslyLoaded) {
              // Return child immediately without fade animation
              return child;
            },
      // Use direct asset loading for better reliability
      errorBuilder: (context, error, stackTrace) {
        if (kDebugMode) {
          debugPrint('Failed to load image: $imagePath, error: $error');
        }
        return errorWidget ??
            Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.deepPurple.shade400,
                    Colors.purple.shade600,
                    Colors.pink.shade500,
                  ],
                ),
              ),
              child: Center(
                child: Icon(Icons.favorite, color: Colors.white54, size: 48),
              ),
            );
      },
    );
  }
}

/// Fast background image widget specifically for post backgrounds
class FastBackgroundImage extends StatelessWidget {
  final String imagePath;
  final Widget child;
  final BoxFit fit;

  const FastBackgroundImage({
    super.key,
    required this.imagePath,
    required this.child,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(imagePath),
          fit: fit,
          // No fade animation for instant loading
          filterQuality: FilterQuality.low,
        ),
      ),
      child: child,
    );
  }
}
