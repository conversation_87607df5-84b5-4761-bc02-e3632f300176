// lib/screens/favorites_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:screenshot/screenshot.dart';
import 'package:gal/gal.dart';
import 'dart:ui' as ui;

import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';

import '../utils/storage_permission_manager.dart';
import '../utils/exit_confirmation_utils.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<FavoritesProvider, ThemeProvider>(
      builder: (context, favoritesProvider, themeProvider, child) {
        final favorites = _searchQuery.isEmpty
            ? favoritesProvider.favorites
            : favoritesProvider.searchFavorites(_searchQuery);

        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            await ExitConfirmationUtils.handlePopInvoked(didPop, result, context);
          },
          child: Scaffold(
          appBar: Custom3DAppBar(
            title: 'Favorites (${favoritesProvider.favoritesCount})',
            actions: [
              if (favoritesProvider.favoritesCount > 0)
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'clear_all') {
                      _showClearAllDialog(context, favoritesProvider);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'clear_all',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Clear All'),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
          drawer: AppDrawer(),
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.grey.shade100,
          body: Column(
            children: [
              // Search Bar
              if (favoritesProvider.favoritesCount > 0)
                Container(
                  margin: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: themeProvider.isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Search favorites...',
                      prefixIcon: Icon(Icons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),

              // Favorites List
              Expanded(
                child: favorites.isEmpty
                    ? _buildEmptyState(themeProvider)
                    : ListView.builder(
                        padding: EdgeInsets.fromLTRB(20, 0, 20, 20),
                        itemCount: favorites.length,
                        itemBuilder: (context, index) {
                          final favorite = favorites[index];
                          return Padding(
                            padding: EdgeInsets.only(bottom: 25),
                            child: EnhancedFavoritePostCard(
                              key: ValueKey(favorite.id),
                              favorite: favorite,
                              onRemove: () {
                                favoritesProvider.removeFromFavorites(
                                  favorite.id,
                                );
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Row(
                                      children: [
                                        Icon(Icons.check, color: Colors.white),
                                        SizedBox(width: 12),
                                        Text('Removed from favorites'),
                                      ],
                                    ),
                                    backgroundColor: Colors.orange,
                                    action: SnackBarAction(
                                      label: 'Undo',
                                      textColor: Colors.white,
                                      onPressed: () {
                                        favoritesProvider.addToFavorites(
                                          favorite.id,
                                          favorite.text,
                                          favorite.backgroundImage,
                                        );
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ); // Close PopScope
      },
    );
  }



  Widget _buildEmptyState(ThemeProvider themeProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: themeProvider.isDarkMode
                ? Colors.grey.shade600
                : Colors.grey.shade400,
          ),
          SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? 'No favorites yet' : 'No favorites found',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode
                  ? Colors.grey.shade400
                  : Colors.grey.shade600,
            ),
          ),
          SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Tap the heart icon on posts to add them here'
                : 'Try a different search term',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: themeProvider.isDarkMode
                  ? Colors.grey.shade500
                  : Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(BuildContext context, FavoritesProvider provider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Clear All Favorites'),
          content: Text(
            'Are you sure you want to remove all ${provider.favoritesCount} favorites? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                provider.clearAllFavorites();
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.white),
                        SizedBox(width: 12),
                        Text('All favorites cleared'),
                      ],
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('Clear All'),
            ),
          ],
        );
      },
    );
  }
}

class EnhancedFavoritePostCard extends StatefulWidget {
  final FavoritePost favorite;
  final VoidCallback onRemove;

  const EnhancedFavoritePostCard({
    super.key,
    required this.favorite,
    required this.onRemove,
  });

  @override
  State<EnhancedFavoritePostCard> createState() =>
      _EnhancedFavoritePostCardState();
}

class _EnhancedFavoritePostCardState extends State<EnhancedFavoritePostCard> {
  final ScreenshotController _screenshotController = ScreenshotController();
  bool _isSaving = false;

  String _cleanQuoteText(String text) {
    return text
        .replaceAll(RegExp(r'^[""]'), '')
        .replaceAll(RegExp(r'[""]$'), '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('"', '')
        .replaceAll('"', '')
        .replaceAll(''', '')
        .replaceAll(''', '')
        .trim();
  }

  String _detectLanguage(String text) {
    // Simple language detection based on character patterns
    final hindiPattern = RegExp(r'[\u0900-\u097F]');
    return hindiPattern.hasMatch(text) ? 'Hindi' : 'English';
  }

  Future<void> _saveAsJPG() async {
    if (_isSaving || !mounted) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final permissionManager = StoragePermissionManager.instance;
      bool hasPermission = await permissionManager.hasStoragePermission();
      if (!hasPermission) {
        hasPermission = await permissionManager.requestStoragePermission();
      }

      if (hasPermission && mounted) {
        _showSavingMessage();

        final Uint8List? pngBytes = await _screenshotController.capture(
          delay: Duration(milliseconds: 50),
          pixelRatio: 2.0,
        );

        if (pngBytes != null && mounted) {
          await _processAndSaveImage(pngBytes);
        }
      } else {
        _showPermissionMessage();
      }
    } catch (e) {
      _showErrorMessage(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _processAndSaveImage(Uint8List pngBytes) async {
    final ui.Codec codec = await ui.instantiateImageCodec(pngBytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image image = frameInfo.image;

    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint()..color = Colors.white;

    canvas.drawRect(
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      paint,
    );
    canvas.drawImage(image, Offset.zero, Paint());

    final ui.Picture picture = recorder.endRecording();
    final ui.Image finalImage = await picture.toImage(
      image.width,
      image.height,
    );

    final ByteData? jpgByteData = await finalImage.toByteData(
      format: ui.ImageByteFormat.png,
    );

    if (jpgByteData != null) {
      final Uint8List jpgBytes = jpgByteData.buffer.asUint8List();
      final String fileName =
          "charm_shot_favorite_${DateTime.now().millisecondsSinceEpoch}";

      await Gal.putImageBytes(jpgBytes, name: fileName);
      _showSuccessMessage();
    }
  }

  void _showSavingMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(width: 16),
              Text('Saving image...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showSuccessMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 12),
              Text('Image saved to gallery!'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showPermissionMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Storage permission required to save images'),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () =>
                StoragePermissionManager.instance.openAppSettings(),
          ),
        ),
      );
    }
  }

  void _showErrorMessage(String error) {
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving image: $error'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _copyToClipboard() async {
    try {
      await Clipboard.setData(
        ClipboardData(text: _cleanQuoteText(widget.favorite.text)),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check, color: Colors.white),
                SizedBox(width: 12),
                Text('Quote copied to clipboard!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareQuote() async {
    try {
      await SharePlus.instance.share(
        ShareParams(
          text:
              '${_cleanQuoteText(widget.favorite.text)}\n\n💕 Shared from Charm Shots - The ultimate pickup lines app!',
          subject: 'Check out this pickup line from my favorites!',
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final language = _detectLanguage(widget.favorite.text);
    final cleanedText = _cleanQuoteText(widget.favorite.text);
    final cardHeight = _calculateCardHeight(context);

    return Container(
      height: cardHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(
            height: cardHeight - 80,
            child: _buildContentArea(language, cleanedText),
          ),
          SizedBox(height: 80, child: _buildActionArea()),
        ],
      ),
    );
  }

  double _calculateCardHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return ((screenHeight / 1.8) - 20).clamp(300.0, 500.0);
  }

  Widget _buildContentArea(String language, String cleanedText) {
    return Screenshot(
      controller: _screenshotController,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          image: DecorationImage(
            image: AssetImage(widget.favorite.backgroundImage),
            fit: BoxFit.cover,
            filterQuality: FilterQuality.low,
            colorFilter: ColorFilter.mode(
              Colors.black.withValues(alpha: 0.3),
              BlendMode.darken,
            ),
          ),
        ),
        child: Column(
          children: [
            // Language indicator
            Padding(
              padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: language == 'Hindi'
                          ? Colors.orange.withValues(alpha: 0.8)
                          : Colors.blue.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          language == 'Hindi' ? '🇮🇳' : '🇺🇸',
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(width: 4),
                        Text(
                          language,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Favorite indicator
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.favorite, color: Colors.white, size: 12),
                        SizedBox(width: 4),
                        Text(
                          'Favorite',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 28, vertical: 20),
                  child: Text(
                    cleanedText,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      height: 1.4,
                      letterSpacing: 0.3,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16, right: 16),
              child: Align(
                alignment: Alignment.bottomRight,
                child: Text(
                  '@Charm Shots',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white38,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.3,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionArea() {
    return Container(
      width: double.infinity,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            spreadRadius: 0,
            blurRadius: 2,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: Icons.favorite,
              label: 'Remove',
              isActive: true,
              onTap: widget.onRemove,
            ),
            _buildActionButton(
              icon: _isSaving ? Icons.hourglass_empty : Icons.download,
              label: 'Save',
              onTap: _isSaving ? null : _saveAsJPG,
            ),
            _buildActionButton(
              icon: Icons.copy,
              label: 'Copy',
              onTap: _copyToClipboard,
            ),
            _buildActionButton(
              icon: Icons.share,
              label: 'Share',
              onTap: _shareQuote,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    bool isActive = false,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          splashColor: isActive && label == 'Remove'
              ? Colors.red.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.1),
          child: Container(
            height: 64,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: isActive && label == 'Remove'
                      ? Colors.red
                      : Colors.black87,
                  size: 20,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: isActive && label == 'Remove'
                        ? Colors.red
                        : Colors.black87,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
