// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';

import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../utils/exit_confirmation_utils.dart';

// Premium gradient colors for categories with enhanced visual appeal
List<Map<String, dynamic>> getCategoriesWithPremiumColors(
  ThemeProvider themeProvider,
) {
  final premiumColors = [
    themeProvider.premiumGradientPrimary,
    themeProvider.premiumGradientSecondary,
    themeProvider.premiumGradientAccent,
    [const Color(0xFF667eea), const Color(0xFF764ba2)], // Cosmic Fusion
    [const Color(0xFF43cea2), const Color(0xFF185a9d)], // Endless River
    [const Color(0xFF8360c3), const Color(0xFF2ebf91)], // Mystical Sunset
    [const Color(0xFFf093fb), const Color(0xFFf5576c)], // Sweet Dreams
    [const Color(0xFF4facfe), const Color(0xFF00f2fe)], // Azure Skies
    [const Color(0xFFa8edea), const Color(0xFFfed6e3)], // Soft Pastel
    [const Color(0xFFffecd2), const Color(0xFFfcb69f)], // Golden Hour
    [const Color(0xFFd299c2), const Color(0xFFfef9d7)], // Rose Dreams
    [const Color(0xFF89f7fe), const Color(0xFF66a6ff)], // Ocean Depths
    [const Color(0xFFff9a9e), const Color(0xFFfecfef)], // Blush Pink
    [const Color(0xFF96fbc4), const Color(0xFFf9f586)], // Fresh Mint
  ];

  return [
    {
      'name': 'Romantic',
      'iconAsset': 'assets/icons/romantic.svg',
      'gradientColors': premiumColors[8],
      'shadowColor': premiumColors[8][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Flirty',
      'iconAsset': 'assets/icons/flirty.svg',
      'gradientColors': premiumColors[12],
      'shadowColor': premiumColors[12][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Cute',
      'iconAsset': 'assets/icons/cute.svg',
      'gradientColors': premiumColors[2],
      'shadowColor': premiumColors[2][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Funny',
      'iconAsset': 'assets/icons/funny.svg',
      'gradientColors': premiumColors[13],
      'shadowColor': premiumColors[13][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Bad',
      'icon': Icons.thumb_down_rounded,
      'gradientColors': premiumColors[1],
      'shadowColor': premiumColors[1][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Clever',
      'iconAsset': 'assets/icons/cleaver.svg',
      'gradientColors': premiumColors[3],
      'shadowColor': premiumColors[3][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Genius',
      'iconAsset': 'assets/icons/genius.svg',
      'gradientColors': premiumColors[4],
      'shadowColor': premiumColors[4][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Hookup',
      'iconAsset': 'assets/icons/hookup.svg',
      'gradientColors': premiumColors[7],
      'shadowColor': premiumColors[7][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Dirty',
      'iconAsset': 'assets/icons/dirty.svg',
      'gradientColors': premiumColors[5],
      'shadowColor': premiumColors[5][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Bold',
      'iconAsset': 'assets/icons/bold.svg',
      'gradientColors': premiumColors[0],
      'shadowColor': premiumColors[0][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Nerd',
      'iconAsset': 'assets/icons/nerd.svg',
      'gradientColors': premiumColors[10],
      'shadowColor': premiumColors[10][0].withValues(alpha: 0.3),
    },
    {
      'name': 'Food',
      'iconAsset': 'assets/icons/food.svg',
      'gradientColors': premiumColors[11],
      'shadowColor': premiumColors[11][0].withValues(alpha: 0.3),
    },
  ];
}

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    // Pre-calculate categories to avoid Consumer overhead
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final categories = getCategoriesWithPremiumColors(themeProvider);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        await ExitConfirmationUtils.handlePopInvoked(didPop, result, context);
      },
      child: Scaffold(
      appBar: Custom3DAppBar(
        title: 'Charm Shots',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () => Navigator.pushNamed(context, '/favorites'),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.favorite_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      drawer: const AppDrawer(),
      backgroundColor: const Color(0xFFF8F9FA),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF), Color(0xFFF1F3F4)],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          slivers: [
            // Header section with premium styling
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.fromLTRB(12, 20, 12, 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Choose Your Style',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                            letterSpacing: 0.5,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Select a category to discover perfect pickup lines',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Premium category grid - optimized for performance
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final cat = categories[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 20),
                      child: RepaintBoundary(
                        child: PremiumCategoryCard(
                          key: ValueKey('category_${cat['name']}'),
                          title: cat['name'] as String,
                          icon: cat['icon'] as IconData?,
                          iconAsset: cat['iconAsset'] as String?,
                          gradientColors: cat['gradientColors'] as List<Color>,
                          shadowColor: cat['shadowColor'] as Color,
                          onTap: () =>
                              _navigateToLines(context, cat['name'] as String),
                        ),
                      ),
                    );
                  },
                  childCount: categories.length,
                  addAutomaticKeepAlives: false,
                  addRepaintBoundaries: false,
                  addSemanticIndexes: false,
                ),
              ),
            ),

            // Bottom padding
            const SliverToBoxAdapter(child: SizedBox(height: 30)),
          ],
        ),
      ),
    ); // Close PopScope
  }



  void _navigateToLines(BuildContext context, String categoryName) {
    // Add smooth navigation with hero animation
    Navigator.pushNamed(
      context,
      '/lines',
      arguments: {'category': categoryName, 'language': language},
    );
  }
}

// Premium Category Card Widget
class PremiumCategoryCard extends StatefulWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final List<Color> gradientColors;
  final Color shadowColor;
  final VoidCallback onTap;

  const PremiumCategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    required this.gradientColors,
    required this.shadowColor,
    required this.onTap,
  });

  @override
  State<PremiumCategoryCard> createState() => _PremiumCategoryCardState();
}

class _PremiumCategoryCardState extends State<PremiumCategoryCard> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return AnimatedScale(
      scale: _isPressed ? 0.95 : 1.0,
      duration: const Duration(milliseconds: 100),
      child: Container(
        height: 85,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: widget.gradientColors,
          ),
          boxShadow: [
            BoxShadow(
              color: widget.shadowColor.withValues(
                alpha: _isPressed ? 0.2 : 0.4,
              ),
              blurRadius: _isPressed ? 8 : 15,
              offset: Offset(0, _isPressed ? 4 : 8),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: _isPressed ? 0.05 : 0.1),
              blurRadius: _isPressed ? 5 : 10,
              offset: Offset(0, _isPressed ? 2 : 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: () {
              // Haptic feedback for premium feel
              HapticFeedback.selectionClick();
              widget.onTap();
            },
            onTapDown: (_) => setState(() => _isPressed = true),
            onTapUp: (_) => setState(() => _isPressed = false),
            onTapCancel: () => setState(() => _isPressed = false),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Icon section with premium styling
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: widget.iconAsset != null
                          ? SvgPicture.asset(
                              widget.iconAsset!,
                              width: 28,
                              height: 28,
                              colorFilter: const ColorFilter.mode(
                                Colors.white,
                                BlendMode.srcIn,
                              ),
                            )
                          : Icon(
                              widget.icon ?? Icons.category_rounded,
                              color: Colors.white,
                              size: 28,
                            ),
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Title section
                  Expanded(
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),

                  // Arrow icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
