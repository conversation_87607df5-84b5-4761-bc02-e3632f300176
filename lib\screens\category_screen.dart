// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';

import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../utils/exit_confirmation_utils.dart';
import '../data/categories_data.dart';

// Categories are now managed in a separate data file for better maintainability

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    // Pre-calculate categories to avoid Consumer overhead
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final categories = CategoriesData.getCategoriesWithPremiumColors(
      themeProvider,
    );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        await ExitConfirmationUtils.handlePopInvoked(didPop, result, context);
      },
      child: Scaffold(
        appBar: Custom3DAppBar(
          title: 'Charm Shots',
          actions: [
            Container(
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () => Navigator.pushNamed(context, '/favorites'),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: const Icon(
                      Icons.favorite_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        drawer: const AppDrawer(),
        backgroundColor: const Color(0xFFF8F9FA),
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF), Color(0xFFF1F3F4)],
              stops: [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ),
            slivers: [
              // Header section with premium styling
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.fromLTRB(12, 20, 12, 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Choose Your Style',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                              letterSpacing: 0.5,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Select a category to discover perfect pickup lines',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Premium category grid - optimized for performance
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final cat = categories[index];
                      return Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        child: RepaintBoundary(
                          child: PremiumCategoryCard(
                            key: ValueKey('category_${cat['name']}'),
                            title: cat['name'] as String,
                            icon: cat['icon'] as IconData?,
                            iconAsset: cat['iconAsset'] as String?,
                            gradientColors:
                                cat['gradientColors'] as List<Color>,
                            shadowColor: cat['shadowColor'] as Color,
                            onTap: () => _navigateToLines(
                              context,
                              cat['name'] as String,
                            ),
                          ),
                        ),
                      );
                    },
                    childCount: categories.length,
                    addAutomaticKeepAlives: false,
                    addRepaintBoundaries: false,
                    addSemanticIndexes: false,
                  ),
                ),
              ),

              // Bottom padding
              const SliverToBoxAdapter(child: SizedBox(height: 30)),
            ],
          ),
        ),
      ), // Close Scaffold
    ); // Close PopScope
  }

  void _navigateToLines(BuildContext context, String categoryName) {
    // Add smooth navigation with hero animation
    Navigator.pushNamed(
      context,
      '/lines',
      arguments: {'category': categoryName, 'language': language},
    );
  }
}

// Premium Category Card Widget
class PremiumCategoryCard extends StatefulWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final List<Color> gradientColors;
  final Color shadowColor;
  final VoidCallback onTap;

  const PremiumCategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    required this.gradientColors,
    required this.shadowColor,
    required this.onTap,
  });

  @override
  State<PremiumCategoryCard> createState() => _PremiumCategoryCardState();
}

class _PremiumCategoryCardState extends State<PremiumCategoryCard> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return AnimatedScale(
      scale: _isPressed ? 0.95 : 1.0,
      duration: const Duration(milliseconds: 100),
      child: Container(
        height: 85,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: widget.gradientColors,
          ),
          boxShadow: [
            BoxShadow(
              color: widget.shadowColor.withValues(
                alpha: _isPressed ? 0.2 : 0.4,
              ),
              blurRadius: _isPressed ? 8 : 15,
              offset: Offset(0, _isPressed ? 4 : 8),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: _isPressed ? 0.05 : 0.1),
              blurRadius: _isPressed ? 5 : 10,
              offset: Offset(0, _isPressed ? 2 : 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: () {
              // Haptic feedback for premium feel
              HapticFeedback.selectionClick();
              widget.onTap();
            },
            onTapDown: (_) => setState(() => _isPressed = true),
            onTapUp: (_) => setState(() => _isPressed = false),
            onTapCancel: () => setState(() => _isPressed = false),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Icon section with premium styling
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: widget.iconAsset != null
                          ? SvgPicture.asset(
                              widget.iconAsset!,
                              width: 28,
                              height: 28,
                              colorFilter: const ColorFilter.mode(
                                Colors.white,
                                BlendMode.srcIn,
                              ),
                            )
                          : Icon(
                              widget.icon ?? Icons.category_rounded,
                              color: Colors.white,
                              size: 28,
                            ),
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Title section
                  Expanded(
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),

                  // Arrow icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
