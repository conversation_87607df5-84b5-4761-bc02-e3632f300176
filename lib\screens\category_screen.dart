// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../data/categories_data.dart';

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final categories = CategoriesData.getCategoriesWithPremiumColors(
      themeProvider,
    );

    return Scaffold(
      appBar: Custom3DAppBar(
        title: 'Charm Shots',
        actions: const [], // No heart icon
      ),
      drawer: const AppDrawer(),
      backgroundColor: Colors.white, // White background
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: CustomScrollView(
          cacheExtent: 1000, // Cache items outside viewport for performance
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          slivers: [
            // Top spacing
            const SliverToBoxAdapter(
              child: SizedBox(height: 20),
            ),

            // Premium category grid - optimized with performance enhancements
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final cat = categories[index];
                    return Container(
                      margin:
                          const EdgeInsets.only(bottom: 30), // Increased space
                      child: RepaintBoundary(
                        child: PremiumCategoryCard(
                          key: ValueKey('category_${cat['name']}'),
                          title: cat['name'] as String,
                          icon: cat['icon'] as IconData?,
                          iconAsset: cat['iconAsset'] as String?,
                          gradientColors: cat['gradientColors'] as List<Color>,
                          shadowColor: cat['shadowColor'] as Color,
                          onTap: () =>
                              _navigateToLines(context, cat['name'] as String),
                        ),
                      ),
                    );
                  },
                  childCount: categories.length,
                  addAutomaticKeepAlives: false,
                  addRepaintBoundaries: false,
                  addSemanticIndexes: false,
                ),
              ),
            ),

            // Bottom padding
            const SliverToBoxAdapter(child: SizedBox(height: 30)),
          ],
        ),
      ),
    );
  }

  void _navigateToLines(BuildContext context, String categoryName) {
    Navigator.pushNamed(
      context,
      '/lines',
      arguments: {'category': categoryName, 'language': language},
    );
  }
}

// Optimized Premium Category Card Widget with Animation Controller
class PremiumCategoryCard extends StatefulWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final List<Color> gradientColors;
  final Color shadowColor;
  final VoidCallback onTap;

  const PremiumCategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    required this.gradientColors,
    required this.shadowColor,
    required this.onTap,
  });

  @override
  State<PremiumCategoryCard> createState() => _PremiumCategoryCardState();
}

class _PremiumCategoryCardState extends State<PremiumCategoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 0.4,
      end: 0.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            height: 85,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: widget.gradientColors,
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.shadowColor
                      .withValues(alpha: _shadowAnimation.value),
                  blurRadius: _animationController.isAnimating ? 8 : 15,
                  offset: Offset(0, _animationController.isAnimating ? 4 : 8),
                ),
                BoxShadow(
                  color: Colors.black.withValues(
                    alpha: _animationController.isAnimating ? 0.05 : 0.1,
                  ),
                  blurRadius: _animationController.isAnimating ? 5 : 10,
                  offset: Offset(0, _animationController.isAnimating ? 2 : 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  HapticFeedback.selectionClick();
                  widget.onTap();
                },
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  child: Row(
                    children: [
                      // Icon section with optimized rendering
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: widget.iconAsset != null
                              ? RepaintBoundary(
                                  child: SvgPicture.asset(
                                    widget.iconAsset!,
                                    width: 28,
                                    height: 28,
                                    colorFilter: const ColorFilter.mode(
                                      Colors.white,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                )
                              : Icon(
                                  widget.icon ?? Icons.category_rounded,
                                  color: Colors.white,
                                  size: 28,
                                ),
                        ),
                      ),

                      const SizedBox(width: 20),

                      // Title section
                      Expanded(
                        child: Text(
                          widget.title,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // Arrow icon
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.arrow_forward_ios_rounded,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
